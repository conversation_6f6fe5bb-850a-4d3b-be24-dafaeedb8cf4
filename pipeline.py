#!/usr/bin/env python3
"""
Headless pipeline – final production edition:
1. Extract PDF tracklist → pdf.json
2. Get WAV total duration via header read
3. Compare totals and emit final_report.json
Usage:
    python pipeline.py release.zip
"""
import json
import os
import sys
import tempfile
from pathlib import Path
from dotenv import load_dotenv

import soundfile as sf
from tracklist_extractor import ArchiveProcessor
from compare import run_comparison

dotenv_path = Path(__file__).with_suffix("").parent / ".env"
load_dotenv(dotenv_path=dotenv_path)


def _get_wav_duration(path: Path) -> float:
    """Return duration in seconds (header-only read)."""
    return float(sf.info(path).duration)


def main(archive: Path) -> None:
    if not os.getenv("OPENROUTER_API_KEY"):
        sys.exit("Missing OPENROUTER_API_KEY in .env")

    base = archive.stem

    # 1. PDF tracklist
    processor = ArchiveProcessor(update_status_callback=print)
    pdf_json = processor.process_archive(archive)
    pdf_out = archive.with_name(f"{base}_tracklist.json")
    pdf_out.write_text(json.dumps(pdf_json, indent=2, ensure_ascii=False))
    print(f"✅ {pdf_out}")

    # 2. Quick WAV duration extraction
    with tempfile.TemporaryDirectory() as tmp:
        tmp_path = Path(tmp)
        files = processor._extract_to(archive, tmp_path)
        wavs = [p for p in files if p.suffix.lower() == ".wav"]
        wav_A = next(p for p in wavs if "A" in p.stem.upper())
        wav_B = next(p for p in wavs if "B" in p.stem.upper())

        total_A = _get_wav_duration(wav_A)
        total_B = _get_wav_duration(wav_B)

    # 3. Compare & final report
    report_path = archive.with_name(f"{base}_final_report.json")
    run_comparison(pdf_json, total_A, total_B, report_path)
    print(f"✅ {report_path}")


if __name__ == "__main__":
    if len(sys.argv) != 2:
        sys.exit("Usage: python pipeline.py release.zip")
    main(Path(sys.argv[1]).resolve())