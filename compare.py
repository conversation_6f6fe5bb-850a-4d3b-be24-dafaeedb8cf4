#!/usr/bin/env python3
"""
Ultra-lean PDF vs WAV comparator – production ready.
Checks TOTAL length per side (±10 s) and produces a minimal, clean JSON report.
"""
import json
from pathlib import Path

TOLERANCE_SEC = 10.0  # single source of truth


def mmss_to_sec(mmss: str) -> float:
    """Convert MM:SS → seconds (robust)."""
    try:
        m, s = map(int, str(mmss).split(":"))
        return float(m * 60 + s)
    except Exception:
        return 0.0


def run_comparison(
    pdf_json: dict,
    wav_total_A: float,
    wav_total_B: float,
    out_path: Path,
) -> None:
    """Generate final report."""
    pdf_A = mmss_to_sec(pdf_json["SIDE_A"]["total_duration_mmss"])
    pdf_B = mmss_to_sec(pdf_json["SIDE_B"]["total_duration_mmss"])

    diff_A = abs(pdf_A - wav_total_A)
    diff_B = abs(pdf_B - wav_total_B)

    status_A = "PASS" if diff_A <= TOLERANCE_SEC else "FAIL"
    status_B = "PASS" if diff_B <= TOLERANCE_SEC else "FAIL"

    report = {
        "overall_status": "PASS" if status_A == "PASS" and status_B == "PASS" else "FAIL",
        "tolerance_sec": TOLERANCE_SEC,
        "side_A": {
            "status": status_A,
            "duration_pdf_sec": round(pdf_A, 2),
            "duration_wav_sec": round(wav_total_A, 2),
            "diff_sec": round(diff_A, 2),
        },
        "side_B": {
            "status": status_B,
            "duration_pdf_sec": round(pdf_B, 2),
            "duration_wav_sec": round(wav_total_B, 2),
            "diff_sec": round(diff_B, 2),
        },
    }

    out_path.write_text(json.dumps(report, indent=2, ensure_ascii=False))