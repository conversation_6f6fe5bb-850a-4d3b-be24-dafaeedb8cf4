#!/usr/bin/env python3
"""
Vinyl Validator MCP – produkčn<PERSON> verze
S<PERSON>štění:
  python vinyl_validator_mcp.py           # stdio (pro Claude <PERSON>)
  python vinyl_validator_mcp.py --http    # http na 127.0.0.1:8000
"""
import os
import sys
import json
import time
import asyncio
import tempfile
import logging
from pathlib import Path
from typing import Dict, Any
from contextlib import asynccontextmanager

import soundfile as sf
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt

from mcp.server.fastmcp import FastMCP, Context
from dotenv import load_dotenv

# vlastní logika
from tracklist_extractor import ArchiveProcessor
from compare import run_comparison

# ---------------------------------------------------------
# konfig
# ---------------------------------------------------------
load_dotenv()
API_KEY = os.getenv("OPENROUTER_API_KEY")
if not API_KEY:
    sys.exit("❌  OPENROUTER_API_KEY chybí v prostředí")

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    stream=sys.stderr,
)

logger = logging.getLogger("vinyl-validator")

mcp = FastMCP(
    "vinyl-validator",
    description="Validuje vinyl release: tracklist PDF vs délky WAV",
    version="1.1.0",
)

# ---------------------------------------------------------
# lifespan
# ---------------------------------------------------------
@asynccontextmanager
async def lifespan(server: FastMCP):
    stats = type("Stats", (), {"runs": 0})()
    logger.info("🚀  Vinyl Validator MCP startuje")
    try:
        yield stats
    finally:
        logger.info("📊  Ukončeno")

# ---------------------------------------------------------
# graf
# ---------------------------------------------------------
def _save_comparison_chart(report: dict, output_path: Path) -> None:
    labels = ["SIDE A", "SIDE B"]
    pdf_vals = [report["side_A"]["duration_pdf_sec"], report["side_B"]["duration_pdf_sec"]]
    wav_vals = [report["side_A"]["duration_wav_sec"], report["side_B"]["duration_wav_sec"]]

    x = range(len(labels))
    width = 0.35

    fig, ax = plt.subplots(figsize=(5, 3), dpi=120, facecolor="#f0f0f0")
    ax.bar([i - width / 2 for i in x], pdf_vals, width, label="PDF")
    ax.bar([i + width / 2 for i in x], wav_vals, width, label="WAV")
    ax.set_ylabel("Délka (s)")
    ax.set_title("Porovnání délek stran")
    ax.set_xticks(x)
    ax.set_xticklabels(labels)
    ax.legend()
    plt.tight_layout()
    plt.savefig(output_path, dpi=120, bbox_inches="tight")
    plt.close(fig)

# ---------------------------------------------------------
# tool
# ---------------------------------------------------------
@mcp.tool()
async def validate_vinyl_release(
    archive_path: str,
    ctx: Context,
) -> Dict[str, Any]:
    """Validuje vinyl release: extrahuje tracklist, porovná délky WAV vs PDF."""
    start = time.time()
    stats = ctx.request_context.lifespan_context or type("Stats", (), {"runs": 0})()

    try:
        path = Path(archive_path).expanduser().resolve()
        if not path.exists():
            raise FileNotFoundError("Archiv nebyl nalezen")

        await ctx.info(f"📦  Analyzuji {path.name}")

        processor = ArchiveProcessor(update_status_callback=lambda m: asyncio.create_task(ctx.info(m)))

        # --- 1. PDF tracklist ----
        pdf_json = processor.process_archive(path)

        # --- 2. WAV délky ----
        with tempfile.TemporaryDirectory() as tmp:
            tmp_path = Path(tmp)
            files = processor._extract_to(path, tmp_path)
            wavs = [p for p in files if p.suffix.lower() == ".wav"]
            wav_a = next(p for p in wavs if "A" in p.stem.upper())
            wav_b = next(p for p in wavs if "B" in p.stem.upper())
            total_a = float(sf.info(wav_a).duration)
            total_b = float(sf.info(wav_b).duration)

        # --- 3. srovnání ----
        report_dir = Path(tempfile.gettempdir()) / "vinyl_reports"
        report_dir.mkdir(exist_ok=True)
        report_file = report_dir / f"{path.stem}_final_report.json"
        run_comparison(pdf_json, total_a, total_b, report_file)
        report = json.loads(report_file.read_text(encoding="utf-8"))

        # --- 4. PNG ----
        png_file = report_file.with_suffix(".png")
        _save_comparison_chart(report, png_file)

        stats.runs += 1
        await ctx.info("✅  Dokončeno")

        return {
            "status": "success",
            "archive": path.name,
            "report": report,
            "chart_url": f"file:///{png_file}".replace("\\", "/"),
            "processing_time": round(time.time() - start, 2),
        }

    except Exception as e:
        await ctx.error(str(e))
        return {"status": "error", "message": str(e), "processing_time": round(time.time() - start, 2)}

# ---------------------------------------------------------
# spuštění
# ---------------------------------------------------------
mcp.lifespan = lifespan

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser()
    parser.add_argument("--http", action="store_true", help="spustit HTTP/SSE server")
    parser.add_argument("--host", default="127.0.0.1")
    parser.add_argument("--port", type=int, default=8000)
    args = parser.parse_args()

    if args.http:
        mcp.run(transport="sse")
    else:
        mcp.run(transport="stdio")